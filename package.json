{"name": "deadlock-stats", "version": "0.0.1", "private": true, "main": "index.tsx", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean", "compile": "tsc --noEmit -p . --pretty", "lint": "biome check .", "fmt": "biome format --write .", "align-deps": "npx expo install --fix", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test -e MAESTRO_APP_ID=com.deadlockapi.deadlockstats .maestro/flows", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/native-stack": "^7.3.23", "@shopify/flash-list": "1.8.1", "@tanstack/react-query": "^5.84.0", "apisauce": "3.1.1", "date-fns": "^4.1.0", "expo": "^53.0.20", "expo-application": "~6.1.5", "expo-build-properties": "~0.14.8", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-splash-screen": "~0.30.10", "expo-system-ui": "~5.0.10", "i18next": "^23.16.8", "intl-pluralrules": "^2.0.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-drawer-layout": "^4.1.12", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.18.2", "react-native-mmkv": "^3.3.0", "react-native-reanimated": "~3.17.5", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.1", "react-native-web": "^0.20.0", "services": "link:@/services"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/runtime": "^7.28.2", "@biomejs/biome": "^2.1.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^29.5.14", "@types/react": "~19.0.14", "babel-jest": "^29.7.0", "jest": "~29.7.0", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "reactotron-core-client": "^2.9.7", "reactotron-react-js": "^3.3.16", "reactotron-react-native": "^5.1.14", "reactotron-react-native-mmkv": "^0.2.8", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "engines": {"node": ">=20.0.0"}, "expo": {"install": {"exclude": ["@shopify/flash-list"]}}}